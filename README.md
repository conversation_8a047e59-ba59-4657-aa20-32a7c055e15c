# Solace SEMP v2 Scoped-Gateway — Architecture Flow

The diagram below shows the complete request/response life-cycle:

1. **Swagger UI** fetches a role-trimmed `/spec.json`  
2. The user clicks **Create Queue**  
3. **Gateway**  
   * validates the JWT (Keycloak)  
   * checks the custom role ↔ VPN/verb rule  
   * injects Basic-Auth credentials  
   * proxies to the correct broker host, prefixing `/SEMP/v2/config`  
4. **Solace Broker** executes the call and returns the JSON result  

![PlantUML model](http://plantuml.com:80/plantuml/svg/VLBTJjim5BxtKnnn5IwwGDbLfKxygE87HYDQs4qav3g7uE7OmNPQTTEaFSiURK-ouvXqIxNIYzHsplVpVV6nyzpwfbAC2syid5gpScXPJQTIo9fh3tkp5IzBj71_cUj4wcveVm-ueyFUy3dtkEBhtjarheKo_8KLtFC5TmZ4QHGN6AXUq1BjRF8vW-KGJhBBo1lVC3OUmvnM6hXuRQIJNXeDup5xCm63Jnj-6C7qfF7FGqWu_Gs4AH1IoAxF9lkvLgQKccrc1mJiE4P4Bj2vHzyg9LTVvlkjygn60HQzbRZagY_RHINq-MGEgQFP3r-TqRaE9eoLttWmEu9Jv9OWnCewJ0zErBAWNPY0nCcIJ4ldwCcRedLkNfeQxgOtayUx-vl9BDVag8AWs35kvNcIogF5efs1n6YrXcmw8vkl3JReOhmpYq6sFTePBi8gLpxKsgNVbxN-yKRoJj1SNyscjx0mnPenhZnSwZObn8QD1MiKGaKnfIuPqE__GJuU749ApoE6oY4ye5q4z4Ds6y0Lz-BvNVGHP6XR6QCtYCXrefHPOG59j19UK34zjbuHl2ZWb3if1Y4s9Boef1xLg_29QeirwWBIS8_JvJ0LHZ_9SfiiNcheMjsQtb7oHZCcxJW6FNF3WmD8wNa8eVZTbMmFacd6jF48Pecsmx2_TusAgHqW5yyW6kTD5R-SDwDS__xv2nQjdGlZVDoJzRYucCyp8Cv5tFxJdB1OkA0CGPWTqwgfr1y0)



> **Legend**  
> *JWT check = Keycloak validation* | *Gateway = Express + ROLE_RULES* | *Broker = Solace SEMP v2 Config API*