// server.js  –  scoped-spec gateway + request gatekeeper
import fs          from 'fs';
import yaml        from 'js-yaml';
import express     from 'express';
import { expressjwt } from 'express-jwt';
import jwksRsa     from 'jwks-rsa';
import proxy       from 'http-proxy-middleware';
const { createProxyMiddleware } = proxy;

const SPEC_PATH = '/mnt/f/semp-doc-gateway/semp-v2-swagger-config-oauth.yaml';
const PORT      = 3000;

/*───────────────────────────────────────────────────────────────────────────
  1) <PERSON><PERSON> Swagger spec once
───────────────────────────────────────────────────────────────────────────*/
const fullSpec = yaml.load(fs.readFileSync(SPEC_PATH, 'utf8'));

/*───────────────────────────────────────────────────────────────────────────
  2) Role → permission rule table
     • target   – upstream broker base URL (already includes /SEMP/v2/config)
     • vpn      – msgVpnName this role may touch
     • method   – allowed HTTP verb
     • pathKey  – path template that belongs in the user’s filtered spec
     • basicAuth– "user:password" to reach the broker
───────────────────────────────────────────────────────────────────────────*/
const ROLE_RULES = {
  'queue:create:vpn-a@8080': {
    target:    'http://localhost:8080/SEMP/v2/config',
    vpn:       'vpn-a',
    method:    'post',
    pathKey:   '/msgVpns/{msgVpnName}/queues',
    basicAuth: 'admin:admin'
  },
  'queue:create:vpn-b@8080': {
    target:    'http://localhost:8080/SEMP/v2/config',
    vpn:       'vpn-b',
    method:    'post',
    pathKey:   '/msgVpns/{msgVpnName}/queues',
    basicAuth: 'admin:admin'
  },
  'queue:create:vpn-b@8082': {
    target:    'http://localhost:8082/SEMP/v2/config',
    vpn:       'vpn-b',
    method:    'post',
    pathKey:   '/msgVpns/{msgVpnName}/queues',
    basicAuth: 'admin:admin'
  }
};

/*───────────────────────────────────────────────────────────────────────────
  3) Build Express app + JWT validator
───────────────────────────────────────────────────────────────────────────*/
const app = express();

app.use(
  expressjwt({
    secret: jwksRsa.expressJwtSecret({
      jwksUri: 'http://localhost:8081/realms/semp/protocol/openid-connect/certs',
      cache: true,
      rateLimit: true,
      jwksRequestsPerMinute: 5
    }),
    audience: 'swagger-ui-portal',
    issuer:   'http://localhost:8081/realms/semp',
    algorithms: ['RS256']
  })
);

/*───────────────────────────────────────────────────────────────────────────
  4) /spec.json – return spec trimmed to the single allowed operation
───────────────────────────────────────────────────────────────────────────*/
app.get('/spec.json', (req, res) => {
  const roles = req.auth?.realm_access?.roles || [];
  const role  = roles.find(r => ROLE_RULES[r]);

  if (!role) return res.json({ ...fullSpec, paths: {} });   // nothing allowed

  const rule = ROLE_RULES[role];
  const filtered = {
    [rule.pathKey]: { [rule.method]: fullSpec.paths[rule.pathKey][rule.method] }
  };

  res.json({ ...fullSpec, paths: filtered });
});

/*───────────────────────────────────────────────────────────────────────────
  5) Gatekeeper + proxy for live traffic
───────────────────────────────────────────────────────────────────────────*/
app.use('/msgVpns/:vpnName/queues', (req, res, next) => {
  const roles = req.auth?.realm_access?.roles || [];
  const role  = roles.find(r => ROLE_RULES[r]);
  if (!role) return res.status(403).json({ error: 'No matching role' });

  const rule = ROLE_RULES[role];

  const vpnOk  = req.params.vpnName === rule.vpn;
  const verbOk = req.method.toLowerCase() === rule.method;
  if (!(vpnOk && verbOk)) {
    return res.status(403).json({ error: 'Permission mismatch' });
  }

  // inject broker Basic-Auth
  const encoded = Buffer.from(rule.basicAuth).toString('base64');
  req.headers['authorization'] = `Basic ${encoded}`;

  // proxy to the correct broker
  return createProxyMiddleware({
    target: rule.target,
    changeOrigin: true
  })(req, res, next);
});

/*───────────────────────────────────────────────────────────────────────────
  6) Optional static Swagger-UI files
───────────────────────────────────────────────────────────────────────────*/
app.use('/', express.static('public'));

/*───────────────────────────────────────────────────────────────────────────*/
app.listen(PORT, () =>
  console.log(`Filtered-spec gateway running on http://localhost:${PORT}`)
);
