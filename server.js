// server.js  – gateway + scoped proxy
import fs from 'fs';
import yaml from 'js-yaml';
import express from 'express';
import { expressjwt } from 'express-jwt';
import jwksRsa from 'jwks-rsa';
import proxy from 'http-proxy-middleware';
const { createProxyMiddleware } = proxy;
import swaggerUiDist from 'swagger-ui-dist';
const swaggerUiPath = swaggerUiDist.getAbsoluteFSPath();   // helper provided by the package


const SPEC = yaml.load(fs.readFileSync(
  '/mnt/f/semp-doc-gateway/semp-v2-swagger-config-oauth.yaml', 'utf8'));

const PORT = 3000;

/*── role table ──────────────────────────────────────────────────────────*/
const ROLE_RULES = {
  'queue:create:vpn-a@8080': {
    brokerHost: 'http://localhost:8080',          // ← host *only*
    vpn:        'vpn-a',
    method:     'post',
    pathKey:    '/msgVpns/{msgVpnName}/queues',
    basicAuth:  'admin:admin'
  },
  'queue:create:vpn-b@8080': {
    brokerHost: 'http://localhost:8080',
    vpn:        'vpn-b',
    method:     'post',
    pathKey:    '/msgVpns/{msgVpnName}/queues',
    basicAuth:  'admin:admin'
  },
  'queue:create:vpn-b@8082': {
    brokerHost: 'http://localhost:8082',
    vpn:        'vpn-b',
    method:     'post',
    pathKey:    '/msgVpns/{msgVpnName}/queues',
    basicAuth:  'admin:admin'
  }
};

/*── express app & JWT guard ─────────────────────────────────────────────*/
const app = express();

app.use('/docs', express.static(swaggerUiPath));
app.use('/',     express.static('public'));

app.use(expressjwt({
  secret: jwksRsa.expressJwtSecret({
    jwksUri: 'http://localhost:8081/realms/semp/protocol/openid-connect/certs',
    cache: true, rateLimit: true, jwksRequestsPerMinute: 5
  }),
  audience: 'swagger-ui-portal',
  issuer:   'http://localhost:8081/realms/semp',
  algorithms: ['RS256']
}));

/*── /spec.json : deliver trimmed doc ────────────────────────────────────*/
app.get('/spec.json', (req, res) => {
  const role = (req.auth?.realm_access?.roles || []).find(r => ROLE_RULES[r]);
  if (!role) return res.json({ ...SPEC, paths: {} });

  const rule = ROLE_RULES[role];
  res.json({
    ...SPEC,
    paths: { [rule.pathKey]: { [rule.method]: SPEC.paths[rule.pathKey][rule.method] } }
  });
});

/*── gate + proxy for live traffic ───────────────────────────────────────*/
app.use('/msgVpns/:vpnName/queues', (req, res, next) => {
  const role = (req.auth?.realm_access?.roles || []).find(r => ROLE_RULES[r]);
  if (!role) return res.status(403).json({ error: 'No matching role' });

  const rule = ROLE_RULES[role];
  if (req.params.vpnName !== rule.vpn || req.method.toLowerCase() !== rule.method)
    return res.status(403).json({ error: 'Permission mismatch' });

  // inject Basic-Auth for the broker
  req.headers.authorization = 'Basic ' + Buffer.from(rule.basicAuth).toString('base64');

  // prepend base path *to the original incoming URL*
  req.url = '/SEMP/v2/config' + req.originalUrl;

  return createProxyMiddleware({
    target: rule.brokerHost,
    changeOrigin: true
  })(req, res, next);
});

/*── (optional) static Swagger-UI files ──────────────────────────────────*/
app.use('/', express.static('public'));

app.listen(PORT, () =>
  console.log(`Gateway running on http://localhost:${PORT}`)
);
