// server.js
import fs       from 'fs';
import yaml     from 'js-yaml';
import express  from 'express';
import { expressjwt } from 'express-jwt';
import jwksRsa  from 'jwks-rsa';

const SPEC_PATH = '/mnt/f/semp-doc-gateway/semp-v2-swagger-config-oauth.yaml';
const PORT      = 3000;

// ---- 1) Load the FULL Swagger 2.0 spec once at startup -------------
const fullSpec = yaml.load(fs.readFileSync(SPEC_PATH, 'utf8'));

// ─── Role-to-permission map ─────────────────────────────────────────────
// Each entry: roleName → { host, vpn, method, pathKey }
//   • host      → string expected in req.headers.host        (for live calls)
//   • vpn       → literal vpnName that must match the path param
//   • method    → allowed HTTP verb (lower-case)
//   • pathKey   → OpenAPI path template to keep in /spec.json
// ── Role → rule ──
// • target  → full upstream base URL for that broker
// • vpn     → literal VPN name allowed
// • method  → allowed HTTP verb
const ROLE_RULES = {
  'queue:create:vpn-a@8080': {
    target: 'http://localhost:8080/SEMP/v2/config',
    vpn:    'vpn-a',
    method: 'post',
    basicAuth: 'admin:admin'
  },
  'queue:create:vpn-b@8080': {
    target: 'http://localhost:8080/SEMP/v2/config',
    vpn:    'vpn-b',
    method: 'post',
    basicAuth: 'admin:admin'
  },
  'queue:create:vpn-b@8082': {
    target: 'http://localhost:8082/SEMP/v2/config',
    vpn:    'vpn-b',
    method: 'post',
    basicAuth: 'admin:admin'
  }
};

// ---- 2) Build Express app -----------------------------------------
const app = express();

// ---- 3) JWT validation middleware ---------------------------------
app.use(
  expressjwt({
    secret: jwksRsa.expressJwtSecret({
      jwksUri: 'http://localhost:8081/realms/semp/protocol/openid-connect/certs',  // 👈 your issuer
      cache: true,
      rateLimit: true,
      jwksRequestsPerMinute: 5,
    }),
    audience: 'swagger-ui-portal',          // 👈 your OAuth2 “aud”
    issuer:   'http://localhost:8081/realms/semp',  // 👈 your issuer
    algorithms: ['RS256'],
  })
);

// ---- 4) Spec-filtering route --------------------------------------
app.get('/spec.json', (req, res) => {
  
  // token.scope is a space-separated string per OAuth2 §5.1
  const userScopes = (req.auth?.realm_access?.roles) || [];
  const filteredPaths = {};

  for (const [path, methods] of Object.entries(fullSpec.paths)) {
    const keep = {};
    for (const [verb, op] of Object.entries(methods)) {
      // Each operation now has exactly one oauth2 entry
      const required = (op.security?.find(s => s.oauth2) || {}).oauth2 || [];
      const role = userScopes.find(r => ROLE_RULES[r]);
      let hasAll = false;
      
      if (role) {
        const rule = ROLE_RULES[role];
        const verbMatch  = verb.toLowerCase() === rule.method;
        const pathMatch  = path === rule.pathKey;
        // spec can't know host at design time, so we ignore host here
        hasAll = verbMatch && pathMatch;
      }
      if (hasAll) keep[verb] = op;
    }
    if (Object.keys(keep).length) filteredPaths[path] = keep;
  }

  res.json({ ...fullSpec, paths: filteredPaths });
});

import proxy from 'http-proxy-middleware';
const { createProxyMiddleware } = proxy;

app.use('/msgVpns/:vpnName/queues', (req, res, next) => {
  // 1) pick the *first* role that has a rule
  const roles = req.auth?.realm_access?.roles || [];
  const role  = roles.find(r => ROLE_RULES[r]);
  if (!role) return res.status(403).json({ error: 'No matching role' });

  const rule = ROLE_RULES[role];

  // 2) enforce VPN + HTTP verb
  const vpnOk  = req.params.vpnName === rule.vpn;
  const verbOk = req.method.toLowerCase() === rule.method;
  if (!(vpnOk && verbOk)) {
    return res.status(403).json({ error: 'Permission mismatch' });
  }

  // 3) add Basic-Auth for the upstream broker
  const encoded = Buffer.from(rule.basicAuth).toString('base64');
  req.headers['Authorization'] = `Basic ${encoded}`;

  // 4) proxy straight to the target for *this* role
  return createProxyMiddleware({
    target: rule.target,
    changeOrigin: true
  })(req, res, next);
});

// ---- 5) Static files (optional Swagger UI) -------------------------
app.use('/', express.static('public'));   // later you can drop index.html here

// ---- 6) Start ------------------------------------------------------
app.listen(PORT, () =>
  console.log(`Filtered-spec gateway running on http://localhost:${PORT}`)
);
